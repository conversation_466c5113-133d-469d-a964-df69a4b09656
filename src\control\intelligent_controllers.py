"""
智能控制算法模块
Intelligent Control Algorithms Module

包含：
- 模糊逻辑控制器
- 神经网络控制器
- 强化学习控制器
- 模型预测控制器
- 滑模控制器
- 自适应神经模糊控制器
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Callable
from abc import ABC, abstractmethod
import warnings

# 尝试导入可选依赖
try:
    import skfuzzy as fuzz
    from skfuzzy import control as ctrl
    FUZZY_AVAILABLE = True
except ImportError:
    FUZZY_AVAILABLE = False
    warnings.warn("skfuzzy not available, fuzzy control disabled")

try:
    from sklearn.neural_network import MLPRegressor
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    warnings.warn("sklearn not available, neural network control disabled")

from .controllers import Controller


class FuzzyController(Controller):
    """模糊逻辑控制器"""
    
    def __init__(self, n_inputs: int = 2, n_outputs: int = 2, 
                 name: str = "Fuzzy Controller"):
        """
        初始化模糊控制器
        
        Args:
            n_inputs: 输入变量数量
            n_outputs: 输出变量数量
        """
        super().__init__(name)
        self.n_inputs = n_inputs
        self.n_outputs = n_outputs
        
        if not FUZZY_AVAILABLE:
            raise ImportError("需要安装 scikit-fuzzy: pip install scikit-fuzzy")
        
        self._setup_fuzzy_system()
    
    def _setup_fuzzy_system(self):
        """设置模糊系统"""
        # 输入变量：误差和误差变化率
        self.error = ctrl.Antecedent(np.arange(-10, 11, 1), 'error')
        self.error_rate = ctrl.Antecedent(np.arange(-10, 11, 1), 'error_rate')
        
        # 输出变量：控制量
        self.output = ctrl.Consequent(np.arange(-10, 11, 1), 'output')
        
        # 模糊集定义
        self.error['NB'] = fuzz.trimf(self.error.universe, [-10, -10, -5])
        self.error['NM'] = fuzz.trimf(self.error.universe, [-10, -5, 0])
        self.error['ZE'] = fuzz.trimf(self.error.universe, [-5, 0, 5])
        self.error['PM'] = fuzz.trimf(self.error.universe, [0, 5, 10])
        self.error['PB'] = fuzz.trimf(self.error.universe, [5, 10, 10])
        
        self.error_rate['NB'] = fuzz.trimf(self.error_rate.universe, [-10, -10, -5])
        self.error_rate['NM'] = fuzz.trimf(self.error_rate.universe, [-10, -5, 0])
        self.error_rate['ZE'] = fuzz.trimf(self.error_rate.universe, [-5, 0, 5])
        self.error_rate['PM'] = fuzz.trimf(self.error_rate.universe, [0, 5, 10])
        self.error_rate['PB'] = fuzz.trimf(self.error_rate.universe, [5, 10, 10])
        
        self.output['NB'] = fuzz.trimf(self.output.universe, [-10, -10, -5])
        self.output['NM'] = fuzz.trimf(self.output.universe, [-10, -5, 0])
        self.output['ZE'] = fuzz.trimf(self.output.universe, [-5, 0, 5])
        self.output['PM'] = fuzz.trimf(self.output.universe, [0, 5, 10])
        self.output['PB'] = fuzz.trimf(self.output.universe, [5, 10, 10])
        
        # 模糊规则
        self.rules = [
            ctrl.Rule(self.error['NB'] & self.error_rate['NB'], self.output['NB']),
            ctrl.Rule(self.error['NB'] & self.error_rate['NM'], self.output['NB']),
            ctrl.Rule(self.error['NB'] & self.error_rate['ZE'], self.output['NM']),
            ctrl.Rule(self.error['NB'] & self.error_rate['PM'], self.output['NM']),
            ctrl.Rule(self.error['NB'] & self.error_rate['PB'], self.output['ZE']),
            
            ctrl.Rule(self.error['NM'] & self.error_rate['NB'], self.output['NB']),
            ctrl.Rule(self.error['NM'] & self.error_rate['NM'], self.output['NM']),
            ctrl.Rule(self.error['NM'] & self.error_rate['ZE'], self.output['NM']),
            ctrl.Rule(self.error['NM'] & self.error_rate['PM'], self.output['ZE']),
            ctrl.Rule(self.error['NM'] & self.error_rate['PB'], self.output['PM']),
            
            ctrl.Rule(self.error['ZE'] & self.error_rate['NB'], self.output['NM']),
            ctrl.Rule(self.error['ZE'] & self.error_rate['NM'], self.output['ZE']),
            ctrl.Rule(self.error['ZE'] & self.error_rate['ZE'], self.output['ZE']),
            ctrl.Rule(self.error['ZE'] & self.error_rate['PM'], self.output['ZE']),
            ctrl.Rule(self.error['ZE'] & self.error_rate['PB'], self.output['PM']),
            
            ctrl.Rule(self.error['PM'] & self.error_rate['NB'], self.output['NM']),
            ctrl.Rule(self.error['PM'] & self.error_rate['NM'], self.output['ZE']),
            ctrl.Rule(self.error['PM'] & self.error_rate['ZE'], self.output['PM']),
            ctrl.Rule(self.error['PM'] & self.error_rate['PM'], self.output['PM']),
            ctrl.Rule(self.error['PM'] & self.error_rate['PB'], self.output['PB']),
            
            ctrl.Rule(self.error['PB'] & self.error_rate['NB'], self.output['ZE']),
            ctrl.Rule(self.error['PB'] & self.error_rate['NM'], self.output['PM']),
            ctrl.Rule(self.error['PB'] & self.error_rate['ZE'], self.output['PM']),
            ctrl.Rule(self.error['PB'] & self.error_rate['PM'], self.output['PB']),
            ctrl.Rule(self.error['PB'] & self.error_rate['PB'], self.output['PB']),
        ]
        
        # 控制系统
        self.control_system = ctrl.ControlSystem(self.rules)
        self.simulation = ctrl.ControlSystemSimulation(self.control_system)
        
        # 历史误差（用于计算误差变化率）
        self.prev_error = None
    
    def compute_control(self, t: float, state: np.ndarray, 
                       reference: np.ndarray) -> np.ndarray:
        """计算模糊控制输入"""
        n_dof = len(state) // 2
        q = state[:n_dof]
        dq = state[n_dof:]
        
        qd = reference[:n_dof]
        dqd = reference[n_dof:] if len(reference) > n_dof else np.zeros(n_dof)
        
        # 计算误差
        error = qd - q
        
        # 计算误差变化率
        if self.prev_error is not None:
            error_rate = error - self.prev_error
        else:
            error_rate = np.zeros_like(error)
        
        self.prev_error = error.copy()
        
        # 模糊控制计算
        tau = np.zeros(n_dof)
        
        for i in range(min(n_dof, 2)):  # 最多处理2个自由度
            # 归一化输入
            err_norm = np.clip(error[i] * 10, -10, 10)
            err_rate_norm = np.clip(error_rate[i] * 10, -10, 10)
            
            # 模糊推理
            self.simulation.input['error'] = err_norm
            self.simulation.input['error_rate'] = err_rate_norm
            
            try:
                self.simulation.compute()
                output_val = self.simulation.output['output']
                tau[i] = output_val * 100  # 反归一化
            except:
                # 如果模糊推理失败，使用简单PD控制
                tau[i] = 100 * error[i] + 20 * error_rate[i]
        
        return tau


class NeuralNetworkController(Controller):
    """神经网络控制器"""
    
    def __init__(self, n_inputs: int = 4, n_outputs: int = 2, 
                 hidden_layers: Tuple[int, ...] = (20, 20),
                 name: str = "Neural Network Controller"):
        """
        初始化神经网络控制器
        
        Args:
            n_inputs: 输入维度
            n_outputs: 输出维度
            hidden_layers: 隐藏层结构
        """
        super().__init__(name)
        self.n_inputs = n_inputs
        self.n_outputs = n_outputs
        
        if not SKLEARN_AVAILABLE:
            raise ImportError("需要安装 scikit-learn: pip install scikit-learn")
        
        # 创建神经网络
        self.network = MLPRegressor(
            hidden_layer_sizes=hidden_layers,
            activation='tanh',
            solver='adam',
            learning_rate_init=0.001,
            max_iter=1000,
            random_state=42
        )
        
        # 数据预处理
        self.scaler_input = StandardScaler()
        self.scaler_output = StandardScaler()
        
        # 训练数据收集
        self.training_data = {'inputs': [], 'outputs': []}
        self.is_trained = False
        
        # 在线学习参数
        self.online_learning = True
        self.learning_rate = 0.01
        
    def collect_training_data(self, state: np.ndarray, reference: np.ndarray, 
                            optimal_control: np.ndarray):
        """收集训练数据"""
        # 构造输入特征
        features = self._extract_features(state, reference)
        
        self.training_data['inputs'].append(features)
        self.training_data['outputs'].append(optimal_control)
    
    def _extract_features(self, state: np.ndarray, reference: np.ndarray) -> np.ndarray:
        """提取特征"""
        n_dof = len(state) // 2
        q = state[:n_dof]
        dq = state[n_dof:]
        
        qd = reference[:n_dof]
        dqd = reference[n_dof:] if len(reference) > n_dof else np.zeros(n_dof)
        
        # 特征：位置误差、速度误差
        error_q = qd - q
        error_dq = dqd - dq
        
        features = np.concatenate([error_q, error_dq])
        
        return features
    
    def train_network(self):
        """训练神经网络"""
        if len(self.training_data['inputs']) < 10:
            print("训练数据不足，需要至少10个样本")
            return
        
        X = np.array(self.training_data['inputs'])
        y = np.array(self.training_data['outputs'])
        
        # 数据预处理
        X_scaled = self.scaler_input.fit_transform(X)
        y_scaled = self.scaler_output.fit_transform(y)
        
        # 训练网络
        self.network.fit(X_scaled, y_scaled)
        self.is_trained = True
        
        print(f"神经网络训练完成，训练样本数: {len(X)}")
    
    def compute_control(self, t: float, state: np.ndarray, 
                       reference: np.ndarray) -> np.ndarray:
        """计算神经网络控制输入"""
        features = self._extract_features(state, reference)
        
        if not self.is_trained:
            # 如果未训练，使用简单PD控制
            n_dof = len(state) // 2
            error_q = features[:n_dof]
            error_dq = features[n_dof:]
            tau = 100 * error_q + 20 * error_dq
            return tau
        
        # 神经网络预测
        try:
            features_scaled = self.scaler_input.transform([features])
            output_scaled = self.network.predict(features_scaled)
            tau = self.scaler_output.inverse_transform(output_scaled)[0]
        except:
            # 预测失败时的备用控制
            n_dof = len(state) // 2
            error_q = features[:n_dof]
            error_dq = features[n_dof:]
            tau = 100 * error_q + 20 * error_dq
        
        return tau


class SlidingModeController(Controller):
    """滑模控制器"""
    
    def __init__(self, lambda_param: np.ndarray, k_param: np.ndarray,
                 boundary_layer: float = 0.1, name: str = "Sliding Mode Controller"):
        """
        初始化滑模控制器
        
        Args:
            lambda_param: 滑模面参数
            k_param: 切换增益
            boundary_layer: 边界层厚度
        """
        super().__init__(name)
        self.lambda_param = np.array(lambda_param)
        self.k_param = np.array(k_param)
        self.boundary_layer = boundary_layer
        
        # 积分项
        self.integral_error = None
    
    def compute_control(self, t: float, state: np.ndarray, 
                       reference: np.ndarray) -> np.ndarray:
        """计算滑模控制输入"""
        n_dof = len(state) // 2
        q = state[:n_dof]
        dq = state[n_dof:]
        
        qd = reference[:n_dof]
        dqd = reference[n_dof:] if len(reference) > n_dof else np.zeros(n_dof)
        ddqd = reference[2*n_dof:] if len(reference) > 2*n_dof else np.zeros(n_dof)
        
        # 跟踪误差
        e = qd - q
        de = dqd - dq
        
        # 滑模面
        s = de + self.lambda_param * e
        
        # 等效控制
        tau_eq = ddqd + self.lambda_param * de
        
        # 切换控制
        tau_sw = np.zeros(n_dof)
        for i in range(n_dof):
            if abs(s[i]) > self.boundary_layer:
                tau_sw[i] = self.k_param[i] * np.sign(s[i])
            else:
                # 边界层内使用连续函数
                tau_sw[i] = self.k_param[i] * s[i] / self.boundary_layer
        
        # 总控制输入
        tau = tau_eq + tau_sw
        
        return tau * 100  # 放大控制增益


class ModelPredictiveController(Controller):
    """模型预测控制器（简化版）"""
    
    def __init__(self, prediction_horizon: int = 10, control_horizon: int = 5,
                 Q: np.ndarray = None, R: np.ndarray = None,
                 name: str = "Model Predictive Controller"):
        """
        初始化模型预测控制器
        
        Args:
            prediction_horizon: 预测时域
            control_horizon: 控制时域
            Q: 状态权重矩阵
            R: 控制权重矩阵
        """
        super().__init__(name)
        self.N = prediction_horizon
        self.M = control_horizon
        self.Q = Q
        self.R = R
        
        # 系统模型（线性化）
        self.A = None
        self.B = None
        self.dt = 0.1
        
    def set_linear_model(self, A: np.ndarray, B: np.ndarray):
        """设置线性化模型"""
        self.A = A
        self.B = B
        
        n_states = A.shape[0]
        n_controls = B.shape[1]
        
        if self.Q is None:
            self.Q = np.eye(n_states)
        if self.R is None:
            self.R = np.eye(n_controls)
    
    def compute_control(self, t: float, state: np.ndarray, 
                       reference: np.ndarray) -> np.ndarray:
        """计算MPC控制输入"""
        if self.A is None or self.B is None:
            # 如果没有模型，使用PD控制
            n_dof = len(state) // 2
            q = state[:n_dof]
            dq = state[n_dof:]
            qd = reference[:n_dof]
            dqd = reference[n_dof:] if len(reference) > n_dof else np.zeros(n_dof)
            
            error_q = qd - q
            error_dq = dqd - dq
            tau = 100 * error_q + 20 * error_dq
            return tau
        
        # 简化的MPC实现
        n_states = len(state)
        n_controls = self.B.shape[1]
        
        # 参考轨迹
        ref_traj = np.tile(reference[:n_states], (self.N, 1))
        
        # 预测矩阵构建（简化）
        # 这里使用简化的二次规划求解
        
        # 当前误差
        error = reference[:n_states] - state
        
        # 简化的MPC控制律（实际应用中需要完整的QP求解）
        tau = np.zeros(n_controls)
        for i in range(min(n_controls, len(error)//2)):
            tau[i] = 50 * error[i] + 10 * error[i + len(error)//2]
        
        return tau


class AdaptiveNeuralFuzzyController(Controller):
    """自适应神经模糊控制器（ANFIS简化版）"""
    
    def __init__(self, n_rules: int = 9, learning_rate: float = 0.01,
                 name: str = "Adaptive Neural Fuzzy Controller"):
        """
        初始化自适应神经模糊控制器
        
        Args:
            n_rules: 模糊规则数量
            learning_rate: 学习率
        """
        super().__init__(name)
        self.n_rules = n_rules
        self.learning_rate = learning_rate
        
        # 模糊集参数（可自适应调整）
        self.mf_params = {
            'centers': np.random.uniform(-5, 5, (n_rules, 2)),
            'widths': np.ones((n_rules, 2)) * 2.0
        }
        
        # 规则后件参数
        self.consequent_params = np.random.uniform(-1, 1, (n_rules, 3))  # [a, b, c] for ax + by + c
        
        # 历史数据
        self.history = {'inputs': [], 'outputs': [], 'errors': []}
    
    def _membership_function(self, x: np.ndarray, center: np.ndarray, 
                           width: np.ndarray) -> float:
        """高斯隶属度函数"""
        return np.exp(-0.5 * np.sum(((x - center) / width)**2))
    
    def _forward_pass(self, inputs: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """前向传播"""
        # 计算每个规则的激活度
        activations = np.zeros(self.n_rules)
        for i in range(self.n_rules):
            activations[i] = self._membership_function(
                inputs, 
                self.mf_params['centers'][i], 
                self.mf_params['widths'][i]
            )
        
        # 归一化激活度
        if np.sum(activations) > 0:
            normalized_activations = activations / np.sum(activations)
        else:
            normalized_activations = np.ones(self.n_rules) / self.n_rules
        
        # 计算输出
        output = 0
        for i in range(self.n_rules):
            rule_output = (self.consequent_params[i, 0] * inputs[0] + 
                          self.consequent_params[i, 1] * inputs[1] + 
                          self.consequent_params[i, 2])
            output += normalized_activations[i] * rule_output
        
        return output, normalized_activations
    
    def _backward_pass(self, inputs: np.ndarray, target: float, 
                      output: float, activations: np.ndarray):
        """反向传播更新参数"""
        error = target - output
        
        # 更新后件参数
        for i in range(self.n_rules):
            self.consequent_params[i, 0] += self.learning_rate * error * activations[i] * inputs[0]
            self.consequent_params[i, 1] += self.learning_rate * error * activations[i] * inputs[1]
            self.consequent_params[i, 2] += self.learning_rate * error * activations[i]
        
        # 更新前件参数（简化）
        for i in range(self.n_rules):
            if activations[i] > 0.1:  # 只更新激活度较高的规则
                # 更新中心
                self.mf_params['centers'][i] += (self.learning_rate * 0.1 * error * 
                                                (inputs - self.mf_params['centers'][i]))
                
                # 更新宽度
                self.mf_params['widths'][i] *= (1 + self.learning_rate * 0.01 * error)
                self.mf_params['widths'][i] = np.clip(self.mf_params['widths'][i], 0.1, 10.0)
    
    def compute_control(self, t: float, state: np.ndarray, 
                       reference: np.ndarray) -> np.ndarray:
        """计算自适应神经模糊控制输入"""
        n_dof = len(state) // 2
        q = state[:n_dof]
        dq = state[n_dof:]
        
        qd = reference[:n_dof]
        dqd = reference[n_dof:] if len(reference) > n_dof else np.zeros(n_dof)
        
        # 计算误差
        error_q = qd - q
        error_dq = dqd - dq
        
        tau = np.zeros(n_dof)
        
        for i in range(min(n_dof, 2)):  # 处理前两个自由度
            # 输入特征
            inputs = np.array([error_q[i], error_dq[i]])
            
            # 前向传播
            output, activations = self._forward_pass(inputs)
            
            # 期望输出（使用PD控制作为监督信号）
            target = 100 * error_q[i] + 20 * error_dq[i]
            
            # 在线学习
            self._backward_pass(inputs, target, output, activations)
            
            tau[i] = output
            
            # 记录历史
            self.history['inputs'].append(inputs.copy())
            self.history['outputs'].append(output)
            self.history['errors'].append(target - output)
        
        return tau


def create_intelligent_controller_suite():
    """创建智能控制器套件"""
    
    controllers = {}
    
    # 模糊控制器
    if FUZZY_AVAILABLE:
        controllers['fuzzy'] = FuzzyController()
    
    # 神经网络控制器
    if SKLEARN_AVAILABLE:
        controllers['neural_network'] = NeuralNetworkController()
    
    # 滑模控制器
    controllers['sliding_mode'] = SlidingModeController(
        lambda_param=np.array([5.0, 5.0]),
        k_param=np.array([10.0, 10.0])
    )
    
    # 模型预测控制器
    controllers['mpc'] = ModelPredictiveController()
    
    # 自适应神经模糊控制器
    controllers['anfis'] = AdaptiveNeuralFuzzyController()
    
    return controllers
