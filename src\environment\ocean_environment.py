"""
复杂海洋环境模型
Complex Ocean Environment Model

包含：
- 不规则波浪模型（JONSWAP谱、PM谱）
- 海流模型
- 风载荷模型
- 海洋环境参数变化
- 多方向海况
"""

import numpy as np
import scipy.signal as signal
from typing import Dict, List, Tuple, Optional, Callable
from dataclasses import dataclass
from abc import ABC, abstractmethod


@dataclass
class WaveSpectrum:
    """波浪谱参数"""
    Hs: float  # 有效波高 (m)
    Tp: float  # 谱峰周期 (s)
    gamma: float = 3.3  # 谱峰增强因子
    direction: float = 0.0  # 主波向 (rad)
    spreading: float = 30.0  # 方向分布参数 (deg)


@dataclass
class CurrentProfile:
    """海流剖面"""
    surface_speed: float  # 表面流速 (m/s)
    direction: float  # 流向 (rad)
    depth_decay: float = 0.1  # 深度衰减系数
    profile_type: str = 'exponential'  # 'exponential', 'linear', 'uniform'


@dataclass
class WindCondition:
    """风况参数"""
    speed: float  # 风速 (m/s)
    direction: float  # 风向 (rad)
    gust_factor: float = 1.2  # 阵风因子
    turbulence_intensity: float = 0.1  # 湍流强度


class WaveModel(ABC):
    """波浪模型基类"""

    @abstractmethod
    def generate_wave_elevation(self, t: np.ndarray, x: float = 0, y: float = 0) -> np.ndarray:
        """生成波面高程"""
        pass

    @abstractmethod
    def get_wave_forces(self, t: float, vessel_state: np.ndarray) -> np.ndarray:
        """计算波浪力"""
        pass


class IrregularWaveModel(WaveModel):
    """不规则波浪模型"""

    def __init__(self, spectrum: WaveSpectrum, n_components: int = 100,
                 random_seed: Optional[int] = None):
        """
        初始化不规则波浪模型

        Args:
            spectrum: 波浪谱参数
            n_components: 波浪成分数量
            random_seed: 随机种子
        """
        self.spectrum = spectrum
        self.n_components = n_components

        if random_seed is not None:
            np.random.seed(random_seed)

        # 生成波浪成分
        self._generate_wave_components()

    def _generate_wave_components(self):
        """生成波浪成分"""
        # 频率范围
        f_min = 0.01  # Hz
        f_max = 1.0   # Hz

        # 等间隔频率
        self.frequencies = np.linspace(f_min, f_max, self.n_components)
        self.omega = 2 * np.pi * self.frequencies

        # 计算谱密度
        if hasattr(self, '_jonswap_spectrum'):
            S_f = self._jonswap_spectrum(self.frequencies)
        else:
            S_f = self._pierson_moskowitz_spectrum(self.frequencies)

        # 波幅
        df = self.frequencies[1] - self.frequencies[0]
        self.amplitudes = np.sqrt(2 * S_f * df)

        # 随机相位
        self.phases = np.random.uniform(0, 2*np.pi, self.n_components)

        # 波数（深水近似）
        g = 9.81
        self.wave_numbers = self.omega**2 / g

        # 方向分布
        self.directions = self._generate_directional_distribution()

    def _jonswap_spectrum(self, f: np.ndarray) -> np.ndarray:
        """JONSWAP谱"""
        Hs = self.spectrum.Hs
        Tp = self.spectrum.Tp
        gamma = self.spectrum.gamma

        # 谱峰频率
        fp = 1.0 / Tp

        # JONSWAP参数
        alpha = 5.0 / 16.0 * Hs**2 * fp**4

        # 谱形状参数
        sigma = np.where(f <= fp, 0.07, 0.09)

        # JONSWAP谱
        S_pm = alpha * f**(-5) * np.exp(-5.0/4.0 * (f/fp)**(-4))
        enhancement = gamma ** np.exp(-0.5 * ((f - fp) / (sigma * fp))**2)

        S_jonswap = S_pm * enhancement

        return S_jonswap

    def _pierson_moskowitz_spectrum(self, f: np.ndarray) -> np.ndarray:
        """Pierson-Moskowitz谱"""
        Hs = self.spectrum.Hs
        Tp = self.spectrum.Tp

        fp = 1.0 / Tp
        alpha = 5.0 / 16.0 * Hs**2 * fp**4

        S_pm = alpha * f**(-5) * np.exp(-5.0/4.0 * (f/fp)**(-4))

        return S_pm

    def _generate_directional_distribution(self) -> np.ndarray:
        """生成方向分布"""
        # 主波向
        main_direction = self.spectrum.direction

        # 方向分布（简化的cos^2分布）
        spreading_rad = np.radians(self.spectrum.spreading)

        directions = []
        for _ in range(self.n_components):
            # 在主波向附近生成方向
            direction = main_direction + np.random.normal(0, spreading_rad)
            directions.append(direction)

        return np.array(directions)

    def generate_wave_elevation(self, t: np.ndarray, x: float = 0, y: float = 0) -> np.ndarray:
        """生成波面高程"""
        eta = np.zeros_like(t, dtype=float)

        for i in range(self.n_components):
            k = self.wave_numbers[i]
            omega = self.omega[i]
            A = self.amplitudes[i]
            phi = self.phases[i]
            theta = self.directions[i]

            # 波浪成分
            kx = k * np.cos(theta)
            ky = k * np.sin(theta)

            eta += A * np.cos(kx * x + ky * y - omega * t + phi)

        return eta

    def get_wave_forces(self, t: float, vessel_state: np.ndarray) -> np.ndarray:
        """
        计算波浪力（简化的Morison方程）

        Args:
            t: 时间
            vessel_state: 船舶状态 [x, y, z, roll, pitch, yaw, u, v, w, p, q, r]

        Returns:
            波浪力和力矩 [Fx, Fy, Fz, Mx, My, Mz]
        """
        # 船舶位置
        x, y, z = vessel_state[0:3]

        # 计算当前位置的波浪参数
        wave_forces = np.zeros(6)

        for i in range(self.n_components):
            k = self.wave_numbers[i]
            omega = self.omega[i]
            A = self.amplitudes[i]
            phi = self.phases[i]
            theta = self.directions[i]

            kx = k * np.cos(theta)
            ky = k * np.sin(theta)

            # 波浪速度和加速度
            phase = kx * x + ky * y - omega * t + phi

            # 水质点速度
            u_wave = A * omega * np.cos(phase) * np.exp(k * z)
            v_wave = A * omega * np.sin(phase) * np.exp(k * z)

            # 水质点加速度
            du_wave = -A * omega**2 * np.sin(phase) * np.exp(k * z)
            dv_wave = A * omega**2 * np.cos(phase) * np.exp(k * z)

            # Morison方程（简化）
            rho = 1025  # 海水密度
            Cd = 1.0    # 阻力系数
            Cm = 1.0    # 附加质量系数
            D = 10.0    # 特征尺寸

            # 相对速度
            u_rel = u_wave - vessel_state[6]
            v_rel = v_wave - vessel_state[7]

            # 阻力项
            F_drag_x = 0.5 * rho * Cd * D * u_rel * abs(u_rel)
            F_drag_y = 0.5 * rho * Cd * D * v_rel * abs(v_rel)

            # 惯性项
            F_inertia_x = rho * Cm * np.pi * D**2 / 4 * du_wave
            F_inertia_y = rho * Cm * np.pi * D**2 / 4 * dv_wave

            wave_forces[0] += F_drag_x + F_inertia_x
            wave_forces[1] += F_drag_y + F_inertia_y

            # 简化的力矩计算
            wave_forces[4] += (F_drag_x + F_inertia_x) * (-z)  # 绕y轴力矩
            wave_forces[3] += (F_drag_y + F_inertia_y) * (-z)  # 绕x轴力矩

        return wave_forces


class CurrentModel:
    """海流模型"""

    def __init__(self, profile: CurrentProfile):
        """
        初始化海流模型

        Args:
            profile: 海流剖面参数
        """
        self.profile = profile

    def get_current_velocity(self, z: float) -> Tuple[float, float]:
        """
        获取指定深度的海流速度

        Args:
            z: 深度（负值，海面为0）

        Returns:
            (u_current, v_current): 海流速度分量
        """
        depth = abs(z)

        if self.profile.profile_type == 'exponential':
            speed_factor = np.exp(-self.profile.depth_decay * depth)
        elif self.profile.profile_type == 'linear':
            max_depth = 100.0  # 最大影响深度
            speed_factor = max(0, 1 - depth / max_depth)
        else:  # uniform
            speed_factor = 1.0

        current_speed = self.profile.surface_speed * speed_factor

        u_current = current_speed * np.cos(self.profile.direction)
        v_current = current_speed * np.sin(self.profile.direction)

        return u_current, v_current

    def get_current_forces(self, vessel_state: np.ndarray, vessel_params: Dict) -> np.ndarray:
        """
        计算海流力

        Args:
            vessel_state: 船舶状态
            vessel_params: 船舶参数

        Returns:
            海流力和力矩
        """
        # 船舶位置和速度
        z = vessel_state[2]
        u_vessel, v_vessel = vessel_state[6:8]

        # 海流速度
        u_current, v_current = self.get_current_velocity(z)

        # 相对速度
        u_rel = u_current - u_vessel
        v_rel = v_current - v_vessel

        # 海流力计算（简化）
        rho = 1025  # 海水密度
        Cd_current = vessel_params.get('Cd_current', 0.5)
        A_x = vessel_params.get('A_x', 100.0)  # x方向投影面积
        A_y = vessel_params.get('A_y', 500.0)  # y方向投影面积

        F_current_x = 0.5 * rho * Cd_current * A_x * u_rel * abs(u_rel)
        F_current_y = 0.5 * rho * Cd_current * A_y * v_rel * abs(v_rel)

        current_forces = np.zeros(6)
        current_forces[0] = F_current_x
        current_forces[1] = F_current_y

        # 简化的力矩
        L_pp = vessel_params.get('L_pp', 100.0)  # 船长
        current_forces[5] = F_current_y * L_pp / 4  # 绕z轴力矩

        return current_forces


class WindModel:
    """风载荷模型"""

    def __init__(self, condition: WindCondition):
        """
        初始化风载荷模型

        Args:
            condition: 风况参数
        """
        self.condition = condition
        self.turbulence_components = self._generate_turbulence()

    def _generate_turbulence(self) -> Dict:
        """生成风湍流成分"""
        # 简化的Kaimal谱湍流模型
        n_components = 50
        f_max = 1.0  # Hz

        frequencies = np.logspace(-3, np.log10(f_max), n_components)

        # Kaimal谱参数
        z = 50.0  # 参考高度
        u_star = self.condition.speed * 0.1  # 摩擦速度

        # 湍流谱
        S_u = np.zeros(n_components)
        for i, f in enumerate(frequencies):
            n = f * z / self.condition.speed
            S_u[i] = (4 * u_star**2 * n) / ((1 + 6 * n)**(5/3))

        # 生成湍流时间序列参数
        df = np.diff(frequencies)
        df = np.append(df, df[-1])

        amplitudes = np.sqrt(2 * S_u * df)
        phases = np.random.uniform(0, 2*np.pi, n_components)

        return {
            'frequencies': frequencies,
            'amplitudes': amplitudes,
            'phases': phases
        }

    def get_wind_speed(self, t: float, height: float = 50.0) -> Tuple[float, float]:
        """
        获取风速（包含湍流）

        Args:
            t: 时间
            height: 高度

        Returns:
            (u_wind, v_wind): 风速分量
        """
        # 平均风速
        base_speed = self.condition.speed

        # 高度修正（对数律）
        z0 = 0.01  # 粗糙度长度
        speed_factor = np.log(height / z0) / np.log(50.0 / z0)
        mean_speed = base_speed * speed_factor

        # 湍流成分
        turbulence = 0
        for i in range(len(self.turbulence_components['frequencies'])):
            f = self.turbulence_components['frequencies'][i]
            A = self.turbulence_components['amplitudes'][i]
            phi = self.turbulence_components['phases'][i]

            turbulence += A * np.cos(2 * np.pi * f * t + phi)

        # 总风速
        total_speed = mean_speed + turbulence * self.condition.turbulence_intensity * mean_speed

        # 阵风效应
        gust = 1 + (self.condition.gust_factor - 1) * np.sin(0.1 * t)**2
        total_speed *= gust

        u_wind = total_speed * np.cos(self.condition.direction)
        v_wind = total_speed * np.sin(self.condition.direction)

        return u_wind, v_wind

    def get_wind_forces(self, t: float, vessel_state: np.ndarray,
                       vessel_params: Dict) -> np.ndarray:
        """
        计算风载荷

        Args:
            t: 时间
            vessel_state: 船舶状态
            vessel_params: 船舶参数

        Returns:
            风载荷和力矩
        """
        # 船舶速度
        u_vessel, v_vessel = vessel_state[6:8]
        yaw = vessel_state[5]

        # 风速
        height = vessel_params.get('wind_height', 50.0)
        u_wind, v_wind = self.get_wind_speed(t, height)

        # 相对风速
        u_rel = u_wind - u_vessel
        v_rel = v_wind - v_vessel

        # 转换到船体坐标系
        cos_yaw, sin_yaw = np.cos(yaw), np.sin(yaw)
        u_rel_body = u_rel * cos_yaw + v_rel * sin_yaw
        v_rel_body = -u_rel * sin_yaw + v_rel * cos_yaw

        # 相对风速和风向
        V_rel = np.sqrt(u_rel_body**2 + v_rel_body**2)
        wind_angle = np.arctan2(v_rel_body, u_rel_body)

        # 风载荷系数（简化）
        rho_air = 1.225  # 空气密度

        # 纵向风载荷
        Cx = vessel_params.get('Cx', 0.5)
        A_x_wind = vessel_params.get('A_x_wind', 200.0)
        F_wind_x = 0.5 * rho_air * Cx * A_x_wind * V_rel * u_rel_body

        # 横向风载荷
        Cy = vessel_params.get('Cy', 1.0)
        A_y_wind = vessel_params.get('A_y_wind', 1000.0)
        F_wind_y = 0.5 * rho_air * Cy * A_y_wind * V_rel * v_rel_body

        # 风载荷力矩
        L_pp = vessel_params.get('L_pp', 100.0)
        h_wind = vessel_params.get('h_wind', 30.0)  # 风载荷作用点高度

        M_wind_z = F_wind_y * L_pp / 4  # 偏航力矩
        M_wind_x = F_wind_y * h_wind    # 横摇力矩

        wind_forces = np.zeros(6)
        wind_forces[0] = F_wind_x
        wind_forces[1] = F_wind_y
        wind_forces[3] = M_wind_x
        wind_forces[5] = M_wind_z

        return wind_forces


class OceanEnvironment:
    """综合海洋环境模型"""

    def __init__(self, wave_model: WaveModel, current_model: CurrentModel,
                 wind_model: WindModel):
        """
        初始化海洋环境

        Args:
            wave_model: 波浪模型
            current_model: 海流模型
            wind_model: 风载荷模型
        """
        self.wave_model = wave_model
        self.current_model = current_model
        self.wind_model = wind_model

    def get_environmental_forces(self, t: float, vessel_state: np.ndarray,
                                vessel_params: Dict) -> np.ndarray:
        """
        获取总环境力

        Args:
            t: 时间
            vessel_state: 船舶状态
            vessel_params: 船舶参数

        Returns:
            总环境力和力矩
        """
        # 波浪力
        F_wave = self.wave_model.get_wave_forces(t, vessel_state)

        # 海流力
        F_current = self.current_model.get_current_forces(vessel_state, vessel_params)

        # 风载荷
        F_wind = self.wind_model.get_wind_forces(t, vessel_state, vessel_params)

        # 总环境力
        F_total = F_wave + F_current + F_wind

        return F_total

    def get_environment_info(self, t: float) -> Dict:
        """获取环境信息"""
        info = {
            'wave_spectrum': self.wave_model.spectrum,
            'current_profile': self.current_model.profile,
            'wind_condition': self.wind_model.condition,
            'time': t
        }

        return info


def create_typical_sea_states():
    """创建典型海况"""

    sea_states = {
        'calm': {
            'wave': WaveSpectrum(Hs=0.5, Tp=6.0, direction=0.0),
            'current': CurrentProfile(surface_speed=0.2, direction=0.0),
            'wind': WindCondition(speed=5.0, direction=0.0)
        },
        'moderate': {
            'wave': WaveSpectrum(Hs=2.0, Tp=8.0, direction=np.pi/6),
            'current': CurrentProfile(surface_speed=0.5, direction=np.pi/4),
            'wind': WindCondition(speed=12.0, direction=np.pi/8)
        },
        'rough': {
            'wave': WaveSpectrum(Hs=4.0, Tp=10.0, direction=np.pi/3),
            'current': CurrentProfile(surface_speed=1.0, direction=np.pi/2),
            'wind': WindCondition(speed=20.0, direction=np.pi/4)
        },
        'severe': {
            'wave': WaveSpectrum(Hs=6.0, Tp=12.0, direction=np.pi/2),
            'current': CurrentProfile(surface_speed=1.5, direction=2*np.pi/3),
            'wind': WindCondition(speed=30.0, direction=np.pi/3)
        }
    }

    return sea_states
