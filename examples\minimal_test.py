"""
最小测试
Minimal Test

验证基本导入和创建功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

print("开始最小测试...")

try:
    print("1. 测试numpy导入...")
    import numpy as np
    print("✓ numpy导入成功")
    
    print("2. 测试sympy导入...")
    import sympy as sp
    print("✓ sympy导入成功")
    
    print("3. 测试matplotlib导入...")
    import matplotlib
    matplotlib.use('Agg')  # 非交互式后端
    import matplotlib.pyplot as plt
    print("✓ matplotlib导入成功")
    
    print("4. 测试起重机模块导入...")
    from dynamics.crane_arm import DoubleArmCrane
    print("✓ DoubleArmCrane导入成功")
    
    print("5. 创建起重机对象...")
    crane = DoubleArmCrane()
    print("✓ DoubleArmCrane对象创建成功")
    
    print("6. 设置参数...")
    crane.set_parameters()
    print("✓ 参数设置成功")
    
    print("7. 测试运动学...")
    test_angles = np.array([0.1, 0.2])
    end_pos = crane.get_end_effector_position(test_angles)
    print(f"✓ 正运动学测试成功: {end_pos}")
    
    print("8. 测试控制器导入...")
    from control.controllers import PDController
    print("✓ PDController导入成功")
    
    print("9. 创建控制器...")
    controller = PDController([10, 5], [2, 1])
    print("✓ PDController创建成功")
    
    print("\n🎉 最小测试全部通过！")
    print("基础功能正常工作。")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
