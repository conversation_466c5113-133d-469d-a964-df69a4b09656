"""
调试动力学推导
Debug Dynamics Derivation

调试符号推导和数值化过程中的问题
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

import numpy as np
import sympy as sp
from dynamics.crane_arm import DoubleArmCrane

def debug_dynamics():
    """调试动力学推导过程"""
    print("=== 调试动力学推导 ===\n")
    
    # 1. 创建起重机
    print("1. 创建起重机...")
    crane = DoubleArmCrane()
    
    # 2. 设置参数
    print("2. 设置参数...")
    params = {
        'l1': 2.0, 'l2': 1.5, 'm1': 50.0, 'm2': 30.0, 'mL': 100.0,
        'I1': 20.0, 'I2': 10.0, 'g': 9.81, 'lc1': 1.0, 'lc2': 0.75
    }
    crane.set_parameters(**params)
    
    # 3. 推导动力学
    print("3. 推导动力学...")
    try:
        L = crane.derive_lagrangian()
        print("✓ 拉格朗日量推导成功")
        
        EOM = crane.derive_equations_of_motion()
        print("✓ 运动方程推导成功")
        
        print(f"参数符号: {list(crane.param_symbols.keys())}")
        print(f"参数值: {params}")
        
    except Exception as e:
        print(f"❌ 动力学推导失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 4. 检查符号表达式
    print("\n4. 检查符号表达式...")
    print("质量矩阵 M[0,0]:")
    print(crane.M_symbolic[0, 0])
    
    print("\n重力向量 G[0]:")
    print(crane.G_symbolic[0])
    
    # 5. 手动替换参数
    print("\n5. 手动替换参数...")
    subs_dict = {}
    for param_name, param_value in params.items():
        if param_name in crane.param_symbols:
            subs_dict[crane.param_symbols[param_name]] = param_value
            print(f"  {param_name}: {crane.param_symbols[param_name]} -> {param_value}")
    
    # 测试替换
    M_test = crane.M_symbolic[0, 0].subs(subs_dict)
    print(f"\n替换后的 M[0,0]: {M_test}")
    print(f"是否为数值: {M_test.is_number}")
    
    G_test = crane.G_symbolic[0].subs(subs_dict)
    print(f"替换后的 G[0]: {G_test}")
    print(f"是否为数值: {G_test.is_number}")
    
    # 6. 测试lambdify
    print("\n6. 测试lambdify...")
    try:
        # 手动创建lambdify函数
        M_func_test = sp.lambdify(crane.q_symbols, M_test, 'numpy')
        G_func_test = sp.lambdify(crane.q_symbols, G_test, 'numpy')
        
        # 测试数值计算
        test_q = [0.1, 0.2]
        M_val = M_func_test(*test_q)
        G_val = G_func_test(*test_q)
        
        print(f"✓ M[0,0]({test_q}) = {M_val}")
        print(f"✓ G[0]({test_q}) = {G_val}")
        
    except Exception as e:
        print(f"❌ lambdify测试失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 7. 测试完整的lambdify_dynamics
    print("\n7. 测试完整的lambdify_dynamics...")
    try:
        crane.lambdify_dynamics(params)
        print("✓ lambdify_dynamics成功")
        
        # 测试动力学函数
        test_state = np.array([0.1, 0.2, 0.0, 0.0])
        test_tau = np.array([1.0, 0.5])
        
        state_dot = crane.get_state_derivative(0.0, test_state, test_tau)
        print(f"✓ 状态导数计算成功: {state_dot}")
        
    except Exception as e:
        print(f"❌ lambdify_dynamics失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🎉 动力学调试完成！")

if __name__ == "__main__":
    debug_dynamics()
