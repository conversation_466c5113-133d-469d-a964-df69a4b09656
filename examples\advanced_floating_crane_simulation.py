"""
高级浮吊系统仿真示例
Advanced Floating Crane System Simulation Example

展示扩展功能：
- 多自由度起重机
- 复杂海洋环境
- 智能控制算法
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
import warnings
warnings.filterwarnings('ignore')

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入扩展模块
from dynamics.multi_dof_crane import MultiDOFCrane, create_crane_configurations
from environment.ocean_environment import (
    IrregularWaveModel, CurrentModel, WindModel, OceanEnvironment,
    WaveSpectrum, CurrentProfile, WindCondition, create_typical_sea_states
)
from control.intelligent_controllers import create_intelligent_controller_suite
from floating_crane_system import FloatingCrane6DOF


class AdvancedFloatingCrane(FloatingCrane6DOF):
    """高级浮吊系统"""

    def __init__(self, mass, inertia_tensor, damping, added_mass, hydrostatic,
                 crane_system, ocean_environment, vessel_params):
        """
        初始化高级浮吊系统

        Args:
            crane_system: 多自由度起重机系统
            ocean_environment: 海洋环境模型
            vessel_params: 船舶参数
        """
        # 创建基本的crane_params用于父类初始化
        crane_params = {
            'controller_type': 'PD',
            'Kp': np.array([1000, 500]),
            'Kd': np.array([200, 100]),
            'coupling_enabled': True
        }

        super().__init__(mass, inertia_tensor, damping, added_mass, hydrostatic, crane_params)

        # 替换为高级组件
        self.advanced_crane = crane_system
        self.ocean_env = ocean_environment
        self.vessel_params = vessel_params

        # 智能控制器
        self.intelligent_controllers = create_intelligent_controller_suite()
        self.active_controller = 'sliding_mode'  # 默认使用滑模控制

    def set_intelligent_controller(self, controller_name: str):
        """设置智能控制器"""
        if controller_name in self.intelligent_controllers:
            self.active_controller = controller_name
            print(f"切换到智能控制器: {controller_name}")
        else:
            print(f"控制器 {controller_name} 不可用")

    def dynamics(self, t, state):
        """
        高级动力学方程

        Args:
            t: 时间
            state: 状态向量 [η(6), v(6), q(n_crane), dq(n_crane)]

        Returns:
            状态导数
        """
        # 状态分解
        eta = state[0:6]      # 浮体姿态
        v = state[6:12]       # 浮体速度

        # 起重机状态（根据起重机自由度确定）
        crane_dof = self.advanced_crane.n_dof
        q = state[12:12+crane_dof]      # 起重机关节角度
        dq = state[12+crane_dof:12+2*crane_dof]  # 起重机关节角速度

        # 起重机控制
        qd, dqd = self.get_crane_reference(t)

        # 使用智能控制器
        if self.active_controller in self.intelligent_controllers:
            controller = self.intelligent_controllers[self.active_controller]
            try:
                tau = controller.compute_control(t,
                                               np.concatenate([q, dq]),
                                               np.concatenate([qd, dqd]))
                # 确保输出维度正确
                if len(tau) < crane_dof:
                    tau = np.pad(tau, (0, crane_dof - len(tau)), 'constant')
                elif len(tau) > crane_dof:
                    tau = tau[:crane_dof]
            except Exception as e:
                print(f"智能控制器失败，使用备用PD控制: {e}")
                # 备用PD控制
                error_q = qd - q
                error_dq = dqd - dq
                tau = 100 * error_q + 20 * error_dq
        else:
            # 备用PD控制
            error_q = qd - q
            error_dq = dqd - dq
            tau = 100 * error_q + 20 * error_dq

        # 起重机动力学（简化为双摆）
        if hasattr(self.advanced_crane, 'get_state_derivative'):
            ddq = self.advanced_crane.get_state_derivative(t,
                                                         np.concatenate([q, dq]),
                                                         tau)[crane_dof:]
        else:
            # 简化的起重机动力学
            ddq = 0.1 * tau - 0.5 * dq - 9.81 * np.sin(q)

        # 浮体动力学
        M = self.M_total()
        F_rest = self.K_restoring(eta)
        F_damp = self.C_damping(v)

        # 海洋环境力
        F_env = self.ocean_env.get_environmental_forces(t,
                                                       np.concatenate([eta, v]),
                                                       self.vessel_params)

        # 起重机耦合力
        F_crane = self.crane_coupling_force(eta, v, q, dq, ddq)

        # 浮体加速度
        dv = np.linalg.solve(M, F_rest + F_damp + F_env + F_crane)

        # 姿态运动学
        deta = v.copy()

        return np.concatenate([deta, dv, dq, ddq])

    def get_crane_reference(self, t: float):
        """获取起重机参考轨迹"""
        # 复杂的轨迹规划
        n_dof = self.advanced_crane.n_dof

        if t < 20:
            # 第一阶段：缓慢移动
            progress = t / 20
            qd_full = np.array([0.2 * progress, 0.1 * progress, 0.05 * progress, 0.02 * progress])
            dqd_full = np.array([0.01, 0.005, 0.0025, 0.001])
        elif t < 40:
            # 第二阶段：快速移动
            progress = (t - 20) / 20
            qd_full = np.array([0.2 + 0.3 * progress, 0.1 + 0.2 * progress,
                               0.05 + 0.1 * progress, 0.02 + 0.05 * progress])
            dqd_full = np.array([0.015, 0.01, 0.005, 0.002])
        else:
            # 第三阶段：保持位置
            qd_full = np.array([0.5, 0.3, 0.15, 0.07])
            dqd_full = np.zeros(4)

        # 确保维度匹配
        qd = qd_full[:n_dof]
        dqd = dqd_full[:n_dof]

        return qd, dqd


def create_advanced_system():
    """创建高级浮吊系统"""

    # 1. 创建多自由度起重机
    print("创建多自由度起重机...")
    crane_configs = create_crane_configurations()
    crane_system = crane_configs['4DOF_基座旋转三臂']  # 选择4DOF配置

    # 设置起重机参数
    crane_system.set_parameters(
        I_base=2000.0,
        l1=15.0, l2=12.0, l3=8.0,
        m1=800.0, m2=600.0, m3=400.0,
        I1=1500.0, I2=1000.0, I3=500.0,
        lc1=7.5, lc2=6.0, lc3=4.0,
        mL=2000.0
    )

    crane_system.print_system_info()

    # 2. 创建复杂海洋环境
    print("\n创建复杂海洋环境...")
    sea_states = create_typical_sea_states()
    sea_condition = sea_states['moderate']  # 选择中等海况

    # 波浪模型
    wave_model = IrregularWaveModel(
        spectrum=sea_condition['wave'],
        n_components=50,
        random_seed=42
    )

    # 海流模型
    current_model = CurrentModel(sea_condition['current'])

    # 风载荷模型
    wind_model = WindModel(sea_condition['wind'])

    # 综合海洋环境
    ocean_env = OceanEnvironment(wave_model, current_model, wind_model)

    # 3. 浮体参数
    mass = 60000.0  # 60吨
    inertia_tensor = np.diag([1.5e6, 3e6, 2e6])
    damping = np.diag([1500, 1500, 3000, 8000, 12000, 5000])
    added_mass = np.diag([8000, 8000, 15000, 3000, 8000, 2000])
    hydrostatic = np.diag([0, 0, 80000, 300000, 500000, 0])

    # 船舶参数
    vessel_params = {
        'L_pp': 120.0,      # 船长
        'B': 30.0,          # 船宽
        'T': 8.0,           # 吃水
        'Cd_current': 0.6,  # 海流阻力系数
        'A_x': 150.0,       # x方向投影面积
        'A_y': 800.0,       # y方向投影面积
        'Cx': 0.4,          # 风载荷系数x
        'Cy': 0.8,          # 风载荷系数y
        'A_x_wind': 300.0,  # 风载荷面积x
        'A_y_wind': 1500.0, # 风载荷面积y
        'wind_height': 60.0, # 风载荷作用高度
        'h_wind': 40.0      # 风载荷力臂
    }

    # 4. 创建高级浮吊系统
    advanced_system = AdvancedFloatingCrane(
        mass, inertia_tensor, damping, added_mass, hydrostatic,
        crane_system, ocean_env, vessel_params
    )

    return advanced_system


def simulate_advanced_system():
    """仿真高级系统"""
    print("=== 高级浮吊系统仿真 ===\n")

    # 创建系统
    system = create_advanced_system()

    # 测试不同的智能控制器
    controllers_to_test = ['sliding_mode']  # 可以添加其他控制器

    results = {}

    for controller_name in controllers_to_test:
        print(f"\n测试控制器: {controller_name}")

        # 设置控制器
        system.set_intelligent_controller(controller_name)

        # 仿真参数
        t_end = 60.0
        dt = 0.1
        t_eval = np.arange(0, t_end, dt)

        # 初始状态 [η(6), v(6), q(4), dq(4)] = 16维
        initial_state = np.zeros(16)
        initial_state[3] = 0.05  # 初始横摇
        initial_state[4] = 0.03  # 初始纵摇
        initial_state[12] = 0.1  # 基座角度
        initial_state[13] = 0.05 # 第一臂角度

        try:
            # 执行仿真
            solution = solve_ivp(
                system.dynamics,
                [0, t_end],
                initial_state,
                t_eval=t_eval,
                method='RK45',
                rtol=1e-6,
                atol=1e-9,
                max_step=0.5
            )

            if solution.success:
                print(f"✓ {controller_name} 仿真成功")
                results[controller_name] = {
                    't': solution.t,
                    'states': solution.y.T
                }
            else:
                print(f"❌ {controller_name} 仿真失败: {solution.message}")

        except Exception as e:
            print(f"❌ {controller_name} 仿真异常: {e}")

    # 生成对比图表
    if results:
        generate_comparison_plots(results, system)

    return results


def generate_comparison_plots(results: dict, system):
    """生成对比图表"""
    print("\n生成对比图表...")

    fig, axes = plt.subplots(3, 3, figsize=(18, 15))

    colors = ['blue', 'red', 'green', 'orange', 'purple']

    for idx, (controller_name, data) in enumerate(results.items()):
        t = data['t']
        states = data['states']
        color = colors[idx % len(colors)]

        # 分解状态
        eta = states[:, 0:6]      # 浮体姿态
        v = states[:, 6:12]       # 浮体速度
        q = states[:, 12:16]      # 起重机角度（4DOF）

        # 浮体位置
        axes[0, 0].plot(t, eta[:, 0], color=color, label=f'{controller_name} - X')
        axes[0, 0].plot(t, eta[:, 1], '--', color=color, label=f'{controller_name} - Y')
        axes[0, 0].set_title('浮体位置')
        axes[0, 0].set_xlabel('时间 (s)')
        axes[0, 0].set_ylabel('位置 (m)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 浮体姿态
        axes[0, 1].plot(t, np.degrees(eta[:, 3]), color=color, label=f'{controller_name} - Roll')
        axes[0, 1].plot(t, np.degrees(eta[:, 4]), '--', color=color, label=f'{controller_name} - Pitch')
        axes[0, 1].set_title('浮体姿态')
        axes[0, 1].set_xlabel('时间 (s)')
        axes[0, 1].set_ylabel('角度 (度)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 起重机基座旋转
        axes[0, 2].plot(t, np.degrees(q[:, 0]), color=color, label=f'{controller_name} - 基座')
        axes[0, 2].set_title('起重机基座旋转')
        axes[0, 2].set_xlabel('时间 (s)')
        axes[0, 2].set_ylabel('角度 (度)')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)

        # 起重机臂角度
        axes[1, 0].plot(t, np.degrees(q[:, 1]), color=color, label=f'{controller_name} - 臂1')
        axes[1, 0].plot(t, np.degrees(q[:, 2]), '--', color=color, label=f'{controller_name} - 臂2')
        axes[1, 0].plot(t, np.degrees(q[:, 3]), ':', color=color, label=f'{controller_name} - 臂3')
        axes[1, 0].set_title('起重机臂角度')
        axes[1, 0].set_xlabel('时间 (s)')
        axes[1, 0].set_ylabel('角度 (度)')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 浮体速度
        axes[1, 1].plot(t, v[:, 0], color=color, label=f'{controller_name} - u')
        axes[1, 1].plot(t, v[:, 1], '--', color=color, label=f'{controller_name} - v')
        axes[1, 1].set_title('浮体速度')
        axes[1, 1].set_xlabel('时间 (s)')
        axes[1, 1].set_ylabel('速度 (m/s)')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        # 浮体角速度
        axes[1, 2].plot(t, np.degrees(v[:, 3]), color=color, label=f'{controller_name} - p')
        axes[1, 2].plot(t, np.degrees(v[:, 4]), '--', color=color, label=f'{controller_name} - q')
        axes[1, 2].set_title('浮体角速度')
        axes[1, 2].set_xlabel('时间 (s)')
        axes[1, 2].set_ylabel('角速度 (度/s)')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)

        # 浮体运动轨迹
        axes[2, 0].plot(eta[:, 0], eta[:, 1], color=color, linewidth=2,
                       label=f'{controller_name}')
        axes[2, 0].plot(eta[0, 0], eta[0, 1], 'o', color=color, markersize=8)
        axes[2, 0].plot(eta[-1, 0], eta[-1, 1], 's', color=color, markersize=8)
        axes[2, 0].set_title('浮体运动轨迹')
        axes[2, 0].set_xlabel('X (m)')
        axes[2, 0].set_ylabel('Y (m)')
        axes[2, 0].legend()
        axes[2, 0].set_aspect('equal')
        axes[2, 0].grid(True, alpha=0.3)

        # 系统总能量
        vessel_kinetic = 0.5 * system.m * np.sum(v[:, :3]**2, axis=1)
        vessel_potential = system.m * 9.81 * eta[:, 2]
        total_energy = vessel_kinetic + vessel_potential

        axes[2, 1].plot(t, total_energy, color=color, label=f'{controller_name}')
        axes[2, 1].set_title('系统总能量')
        axes[2, 1].set_xlabel('时间 (s)')
        axes[2, 1].set_ylabel('能量 (J)')
        axes[2, 1].legend()
        axes[2, 1].grid(True, alpha=0.3)

        # 性能指标
        rms_position = np.sqrt(np.mean(eta[:, :2]**2, axis=0))
        rms_attitude = np.sqrt(np.mean(eta[:, 3:6]**2, axis=0))

        axes[2, 2].bar([f'{controller_name}\nX', f'{controller_name}\nY'],
                      rms_position, color=color, alpha=0.7)
        axes[2, 2].set_title('RMS位置误差')
        axes[2, 2].set_ylabel('RMS (m)')
        axes[2, 2].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('advanced_floating_crane_results.png', dpi=150, bbox_inches='tight')
    plt.close()
    print("✓ 对比图表保存为 advanced_floating_crane_results.png")


if __name__ == "__main__":
    results = simulate_advanced_system()

    if results:
        print(f"\n🎉 高级浮吊系统仿真完成！")
        print(f"测试了 {len(results)} 种智能控制器")
        print("结果图表已生成，展示了多自由度起重机在复杂海洋环境下的性能")
    else:
        print("❌ 仿真失败，请检查配置")
