"""
多自由度起重机动力学模型
Multi-DOF Crane Dynamics Model

支持任意自由度的串联起重机系统，包括：
- 3DOF起重机（基座旋转 + 双臂）
- 4DOF起重机（基座旋转 + 三臂）
- 5DOF起重机（基座旋转 + 四臂）
- 6DOF起重机（基座旋转 + 五臂）
"""

import numpy as np
import sympy as sp
from typing import Dict, List, Tuple, Optional
from .lagrangian_model import LagrangianSystem


class MultiDOFCrane(LagrangianSystem):
    """
    多自由度起重机系统
    
    支持配置：
    - n_arms: 臂的数量
    - base_rotation: 是否包含基座旋转
    - cable_length: 是否包含缆绳长度控制
    """
    
    def __init__(self, n_arms: int = 3, base_rotation: bool = True, cable_length: bool = False):
        """
        初始化多自由度起重机
        
        Args:
            n_arms: 臂的数量（2-6）
            base_rotation: 是否包含基座旋转自由度
            cable_length: 是否包含缆绳长度控制
        """
        if n_arms < 2 or n_arms > 6:
            raise ValueError("臂的数量必须在2-6之间")
        
        self.n_arms = n_arms
        self.base_rotation = base_rotation
        self.cable_length = cable_length
        
        # 计算总自由度
        total_dof = n_arms
        if base_rotation:
            total_dof += 1
        if cable_length:
            total_dof += 1
        
        super().__init__(total_dof, f"{total_dof}DOF起重机系统")
        
        # 重新定义符号变量以匹配起重机术语
        self._setup_crane_symbols()
        
        # 起重机配置
        self.arm_lengths = []
        self.arm_masses = []
        self.arm_inertias = []
        self.arm_cm_distances = []
        self.load_mass = 0
        self.cable_length_val = 0
        
    def _setup_crane_symbols(self):
        """设置起重机专用符号变量"""
        # 广义坐标符号
        coord_names = []
        
        if self.base_rotation:
            coord_names.append('phi')  # 基座旋转角
        
        for i in range(self.n_arms):
            coord_names.append(f'theta{i+1}')  # 各臂角度
        
        if self.cable_length:
            coord_names.append('l_cable')  # 缆绳长度
        
        # 创建符号
        self.coord_symbols = [sp.Symbol(name) for name in coord_names]
        self.dcoord_symbols = [sp.Symbol(f'd{name}') for name in coord_names]
        self.ddcoord_symbols = [sp.Symbol(f'dd{name}') for name in coord_names]
        self.tau_symbols = [sp.Symbol(f'tau_{name}') for name in coord_names]
        
        # 更新基类的符号
        self.q_symbols = tuple(self.coord_symbols)
        self.dq_symbols = tuple(self.dcoord_symbols)
        self.ddq_symbols = tuple(self.ddcoord_symbols)
        self.tau_symbols = tuple(self.tau_symbols)
    
    def define_parameters(self) -> Dict[str, sp.Symbol]:
        """定义系统参数"""
        params = {}
        
        # 基座参数
        if self.base_rotation:
            params['I_base'] = sp.Symbol('I_base', positive=True)  # 基座转动惯量
        
        # 各臂参数
        for i in range(self.n_arms):
            params[f'l{i+1}'] = sp.Symbol(f'l{i+1}', positive=True)      # 臂长度
            params[f'm{i+1}'] = sp.Symbol(f'm{i+1}', positive=True)      # 臂质量
            params[f'I{i+1}'] = sp.Symbol(f'I{i+1}', positive=True)      # 臂转动惯量
            params[f'lc{i+1}'] = sp.Symbol(f'lc{i+1}', positive=True)    # 臂质心距离
        
        # 负载参数
        params['mL'] = sp.Symbol('mL', positive=True)  # 负载质量
        
        # 缆绳参数
        if self.cable_length:
            params['m_cable'] = sp.Symbol('m_cable', positive=True)  # 缆绳质量
        
        # 重力
        params['g'] = sp.Symbol('g', positive=True)
        
        self.param_symbols.update(params)
        return params
    
    def define_kinematics(self) -> Dict[str, Dict]:
        """定义运动学关系"""
        # 获取参数
        params = self.param_symbols
        
        # 坐标索引
        coord_idx = 0
        
        # 基座旋转角
        if self.base_rotation:
            phi = self.coord_symbols[coord_idx]
            dphi = self.dcoord_symbols[coord_idx]
            coord_idx += 1
        else:
            phi = 0
            dphi = 0
        
        # 各臂角度
        arm_angles = []
        arm_velocities = []
        for i in range(self.n_arms):
            arm_angles.append(self.coord_symbols[coord_idx])
            arm_velocities.append(self.dcoord_symbols[coord_idx])
            coord_idx += 1
        
        # 缆绳长度
        if self.cable_length:
            l_cable = self.coord_symbols[coord_idx]
            dl_cable = self.dcoord_symbols[coord_idx]
        else:
            l_cable = params.get('l_cable_fixed', sp.Symbol('l_cable_fixed', positive=True))
            dl_cable = 0
        
        # === 位置计算 ===
        positions = {}
        velocities = {}
        
        # 基座转动动能
        if self.base_rotation:
            I_base = params['I_base']
            velocities['base_rotation'] = (I_base, dphi**2)
        
        # 计算各臂的位置和速度
        cumulative_angle = phi  # 累积角度（包含基座旋转）
        current_pos = sp.Matrix([0, 0])  # 当前位置
        
        for i in range(self.n_arms):
            arm_idx = i + 1
            l_i = params[f'l{arm_idx}']
            m_i = params[f'm{arm_idx}']
            I_i = params[f'I{arm_idx}']
            lc_i = params[f'lc{arm_idx}']
            
            # 当前臂的绝对角度
            cumulative_angle += arm_angles[i]
            
            # 臂质心位置
            arm_cm_pos = current_pos + sp.Matrix([
                lc_i * sp.cos(cumulative_angle),
                lc_i * sp.sin(cumulative_angle)
            ])
            
            # 臂质心速度
            arm_cm_vel = sp.Matrix([
                sp.diff(arm_cm_pos[0], coord) * vel 
                for coord, vel in zip(self.coord_symbols, self.dcoord_symbols)
            ]).sum()
            
            # 计算速度的平方
            arm_cm_vel_x = sum(sp.diff(arm_cm_pos[0], coord) * vel 
                              for coord, vel in zip(self.coord_symbols, self.dcoord_symbols))
            arm_cm_vel_y = sum(sp.diff(arm_cm_pos[1], coord) * vel 
                              for coord, vel in zip(self.coord_symbols, self.dcoord_symbols))
            
            arm_cm_vel_squared = arm_cm_vel_x**2 + arm_cm_vel_y**2
            
            # 存储位置和速度
            positions[f'arm{arm_idx}_cm'] = (m_i, [arm_cm_pos[0], arm_cm_pos[1]])
            velocities[f'arm{arm_idx}_translation'] = (m_i, arm_cm_vel_squared)
            
            # 臂转动动能
            # 计算臂的角速度
            arm_angular_vel = dphi if self.base_rotation else 0
            for j in range(i + 1):
                arm_angular_vel += arm_velocities[j]
            
            velocities[f'arm{arm_idx}_rotation'] = (I_i, arm_angular_vel**2)
            
            # 更新到臂末端位置
            current_pos += sp.Matrix([
                l_i * sp.cos(cumulative_angle),
                l_i * sp.sin(cumulative_angle)
            ])
        
        # 负载位置和速度
        mL = params['mL']
        
        if self.cable_length:
            # 负载通过缆绳悬挂
            load_pos = current_pos + sp.Matrix([0, -l_cable])
        else:
            # 负载直接连接在末端
            load_pos = current_pos
        
        # 负载速度
        load_vel_x = sum(sp.diff(load_pos[0], coord) * vel 
                        for coord, vel in zip(self.coord_symbols, self.dcoord_symbols))
        load_vel_y = sum(sp.diff(load_pos[1], coord) * vel 
                        for coord, vel in zip(self.coord_symbols, self.dcoord_symbols))
        
        load_vel_squared = load_vel_x**2 + load_vel_y**2
        
        positions['load'] = (mL, [load_pos[0], load_pos[1]])
        velocities['load'] = (mL, load_vel_squared)
        
        # 缆绳动能（如果有）
        if self.cable_length:
            m_cable = params['m_cable']
            # 简化：缆绳质量集中在中点
            cable_cm_pos = current_pos + sp.Matrix([0, -l_cable/2])
            
            cable_vel_x = sum(sp.diff(cable_cm_pos[0], coord) * vel 
                             for coord, vel in zip(self.coord_symbols, self.dcoord_symbols))
            cable_vel_y = sum(sp.diff(cable_cm_pos[1], coord) * vel 
                             for coord, vel in zip(self.coord_symbols, self.dcoord_symbols))
            
            cable_vel_squared = cable_vel_x**2 + cable_vel_y**2
            
            positions['cable'] = (m_cable, [cable_cm_pos[0], cable_cm_pos[1]])
            velocities['cable'] = (m_cable, cable_vel_squared)
        
        return {
            'positions': positions,
            'velocities': velocities
        }
    
    def set_parameters(self, **kwargs):
        """
        设置系统参数的数值
        
        Args:
            **kwargs: 参数名和数值的键值对
        """
        # 默认参数值
        default_params = {
            'g': 9.81,
            'mL': 1000.0,  # 负载质量
        }
        
        # 基座参数
        if self.base_rotation:
            default_params['I_base'] = 1000.0
        
        # 各臂默认参数
        for i in range(self.n_arms):
            arm_idx = i + 1
            default_params[f'l{arm_idx}'] = 10.0 - i * 1.0  # 递减的臂长
            default_params[f'm{arm_idx}'] = 500.0 - i * 50.0  # 递减的质量
            default_params[f'I{arm_idx}'] = 100.0 - i * 10.0  # 递减的转动惯量
            default_params[f'lc{arm_idx}'] = (10.0 - i * 1.0) / 2  # 质心在中点
        
        # 缆绳参数
        if self.cable_length:
            default_params['m_cable'] = 50.0
            default_params['l_cable_fixed'] = 5.0
        
        # 更新参数
        default_params.update(kwargs)
        self.parameters = default_params
        
        # 如果已经推导了符号方程，则生成数值函数
        if hasattr(self, 'M_symbolic') and self.M_symbolic is not None:
            self.lambdify_dynamics(default_params)
    
    def get_end_effector_position(self, q: np.ndarray) -> np.ndarray:
        """
        获取末端执行器位置
        
        Args:
            q: 广义坐标
            
        Returns:
            末端位置 [x, y, z] 或 [x, y]
        """
        if not hasattr(self, 'parameters'):
            raise ValueError("请先调用 set_parameters() 设置系统参数")
        
        coord_idx = 0
        
        # 基座旋转角
        if self.base_rotation:
            phi = q[coord_idx]
            coord_idx += 1
        else:
            phi = 0
        
        # 各臂角度
        arm_angles = q[coord_idx:coord_idx + self.n_arms]
        coord_idx += self.n_arms
        
        # 缆绳长度
        if self.cable_length:
            l_cable = q[coord_idx]
        else:
            l_cable = self.parameters.get('l_cable_fixed', 0)
        
        # 计算末端位置
        cumulative_angle = phi
        current_pos = np.array([0.0, 0.0])
        
        for i, angle in enumerate(arm_angles):
            arm_idx = i + 1
            l_i = self.parameters[f'l{arm_idx}']
            cumulative_angle += angle
            
            current_pos += np.array([
                l_i * np.cos(cumulative_angle),
                l_i * np.sin(cumulative_angle)
            ])
        
        # 添加缆绳长度
        if self.cable_length:
            current_pos += np.array([0, -l_cable])
        
        return current_pos
    
    def get_workspace_analysis(self, n_samples: int = 1000) -> Dict:
        """
        工作空间分析
        
        Args:
            n_samples: 采样点数量
            
        Returns:
            工作空间分析结果
        """
        if not hasattr(self, 'parameters'):
            raise ValueError("请先调用 set_parameters() 设置系统参数")
        
        # 生成随机关节角度
        np.random.seed(42)
        
        # 关节角度范围
        angle_ranges = []
        
        if self.base_rotation:
            angle_ranges.append((-np.pi, np.pi))  # 基座旋转
        
        for i in range(self.n_arms):
            angle_ranges.append((-np.pi/2, np.pi/2))  # 各臂角度
        
        if self.cable_length:
            l_cable_max = self.parameters.get('l_cable_max', 10.0)
            angle_ranges.append((0, l_cable_max))  # 缆绳长度
        
        # 采样
        workspace_points = []
        
        for _ in range(n_samples):
            q_sample = []
            for min_val, max_val in angle_ranges:
                q_sample.append(np.random.uniform(min_val, max_val))
            
            try:
                end_pos = self.get_end_effector_position(np.array(q_sample))
                workspace_points.append(end_pos)
            except:
                continue
        
        workspace_points = np.array(workspace_points)
        
        # 分析结果
        analysis = {
            'points': workspace_points,
            'x_range': [np.min(workspace_points[:, 0]), np.max(workspace_points[:, 0])],
            'y_range': [np.min(workspace_points[:, 1]), np.max(workspace_points[:, 1])],
            'max_reach': np.max(np.linalg.norm(workspace_points, axis=1)),
            'min_reach': np.min(np.linalg.norm(workspace_points, axis=1)),
            'volume': self._estimate_workspace_volume(workspace_points)
        }
        
        return analysis
    
    def _estimate_workspace_volume(self, points: np.ndarray) -> float:
        """估计工作空间体积"""
        if len(points) < 4:
            return 0.0
        
        # 使用凸包估计面积（2D）
        try:
            from scipy.spatial import ConvexHull
            hull = ConvexHull(points)
            return hull.volume  # 在2D中是面积
        except:
            # 简单的边界框估计
            x_range = np.max(points[:, 0]) - np.min(points[:, 0])
            y_range = np.max(points[:, 1]) - np.min(points[:, 1])
            return x_range * y_range
    
    def print_system_info(self):
        """打印系统信息"""
        print(f"\n=== {self.system_name} 系统信息 ===")
        print(f"总自由度: {self.n_dof}")
        print(f"臂数量: {self.n_arms}")
        print(f"基座旋转: {'是' if self.base_rotation else '否'}")
        print(f"缆绳控制: {'是' if self.cable_length else '否'}")
        
        if hasattr(self, 'parameters'):
            print("\n系统参数:")
            for name, value in self.parameters.items():
                print(f"  {name}: {value}")


def create_crane_configurations():
    """创建不同配置的起重机示例"""
    
    configurations = {
        '3DOF_基座旋转双臂': MultiDOFCrane(n_arms=2, base_rotation=True, cable_length=False),
        '4DOF_基座旋转三臂': MultiDOFCrane(n_arms=3, base_rotation=True, cable_length=False),
        '5DOF_基座旋转四臂': MultiDOFCrane(n_arms=4, base_rotation=True, cable_length=False),
        '4DOF_双臂缆绳': MultiDOFCrane(n_arms=2, base_rotation=True, cable_length=True),
        '6DOF_三臂缆绳': MultiDOFCrane(n_arms=3, base_rotation=True, cable_length=True),
    }
    
    return configurations
