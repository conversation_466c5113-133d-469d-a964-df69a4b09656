"""
浮吊系统完整仿真示例
Complete Floating Crane System Simulation Example

演示6自由度浮体与双臂架起重机的耦合仿真
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp

from floating_crane_system import FloatingCrane6DOF, create_default_floating_crane


def simulate_floating_crane():
    """浮吊系统仿真"""
    print("=== 6自由度浮吊系统仿真 ===\n")
    
    # 1. 创建浮吊系统
    print("1. 创建浮吊系统...")
    floating_crane = create_default_floating_crane()
    floating_crane.print_system_info()
    
    # 2. 设置仿真参数
    print("\n2. 设置仿真参数...")
    t_start = 0.0
    t_end = 60.0  # 60秒仿真
    dt = 0.1
    t_eval = np.arange(t_start, t_end, dt)
    
    # 初始状态 [η(6), v(6), q(2), dq(2)]
    initial_state = np.zeros(16)
    # 浮体初始姿态：轻微倾斜
    initial_state[3] = 0.05  # 初始横摇 (roll)
    initial_state[4] = 0.03  # 初始纵摇 (pitch)
    # 起重机初始角度
    initial_state[12] = 0.1  # θ1
    initial_state[13] = 0.05 # θ2
    
    print(f"仿真时间: {t_start} - {t_end} s")
    print(f"时间步长: {dt} s")
    print(f"初始状态维度: {len(initial_state)}")
    
    # 3. 执行仿真
    print("\n3. 执行仿真...")
    
    try:
        solution = solve_ivp(
            floating_crane.dynamics,
            [t_start, t_end],
            initial_state,
            t_eval=t_eval,
            method='RK45',
            rtol=1e-6,
            atol=1e-9,
            max_step=0.5
        )
        
        if solution.success:
            print(f"✓ 仿真成功完成！")
            print(f"  积分步数: {solution.nfev}")
            print(f"  最终时间: {solution.t[-1]:.2f} s")
        else:
            print(f"❌ 仿真失败: {solution.message}")
            return None
            
    except Exception as e:
        print(f"❌ 仿真异常: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    # 4. 分析结果
    print("\n4. 分析仿真结果...")
    
    t = solution.t
    states = solution.y.T
    
    # 分解状态
    eta = states[:, 0:6]      # 浮体姿态
    v = states[:, 6:12]       # 浮体速度
    q = states[:, 12:14]      # 起重机角度
    dq = states[:, 14:16]     # 起重机角速度
    
    # 计算性能指标
    vessel_motion_rms = np.sqrt(np.mean(eta**2, axis=0))
    crane_motion_rms = np.sqrt(np.mean(q**2, axis=0))
    max_vessel_motion = np.max(np.abs(eta), axis=0)
    max_crane_motion = np.max(np.abs(q), axis=0)
    
    print("性能指标:")
    print(f"  浮体运动RMS: {vessel_motion_rms}")
    print(f"  起重机运动RMS: {crane_motion_rms}")
    print(f"  浮体最大运动: {max_vessel_motion}")
    print(f"  起重机最大运动: {max_crane_motion}")
    
    # 5. 生成图表
    print("\n5. 生成结果图表...")
    
    # 创建综合图表
    fig, axes = plt.subplots(3, 3, figsize=(18, 15))
    
    # 浮体位置
    axes[0, 0].plot(t, eta[:, 0], 'b-', label='X')
    axes[0, 0].plot(t, eta[:, 1], 'r-', label='Y')
    axes[0, 0].plot(t, eta[:, 2], 'g-', label='Z')
    axes[0, 0].set_title('浮体位置')
    axes[0, 0].set_xlabel('时间 (s)')
    axes[0, 0].set_ylabel('位置 (m)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 浮体姿态
    axes[0, 1].plot(t, np.degrees(eta[:, 3]), 'b-', label='Roll')
    axes[0, 1].plot(t, np.degrees(eta[:, 4]), 'r-', label='Pitch')
    axes[0, 1].plot(t, np.degrees(eta[:, 5]), 'g-', label='Yaw')
    axes[0, 1].set_title('浮体姿态')
    axes[0, 1].set_xlabel('时间 (s)')
    axes[0, 1].set_ylabel('角度 (deg)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 浮体速度
    axes[0, 2].plot(t, v[:, 0], 'b-', label='u')
    axes[0, 2].plot(t, v[:, 1], 'r-', label='v')
    axes[0, 2].plot(t, v[:, 2], 'g-', label='w')
    axes[0, 2].set_title('浮体线速度')
    axes[0, 2].set_xlabel('时间 (s)')
    axes[0, 2].set_ylabel('速度 (m/s)')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 浮体角速度
    axes[1, 0].plot(t, np.degrees(v[:, 3]), 'b-', label='p')
    axes[1, 0].plot(t, np.degrees(v[:, 4]), 'r-', label='q')
    axes[1, 0].plot(t, np.degrees(v[:, 5]), 'g-', label='r')
    axes[1, 0].set_title('浮体角速度')
    axes[1, 0].set_xlabel('时间 (s)')
    axes[1, 0].set_ylabel('角速度 (deg/s)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 起重机关节角度
    axes[1, 1].plot(t, np.degrees(q[:, 0]), 'b-', label='θ₁')
    axes[1, 1].plot(t, np.degrees(q[:, 1]), 'r-', label='θ₂')
    # 添加参考轨迹
    qd_ref = []
    for time_val in t:
        qd, _ = floating_crane.get_crane_reference(time_val)
        qd_ref.append(qd)
    qd_ref = np.array(qd_ref)
    axes[1, 1].plot(t, np.degrees(qd_ref[:, 0]), 'b--', alpha=0.7, label='θ₁ᵈ')
    axes[1, 1].plot(t, np.degrees(qd_ref[:, 1]), 'r--', alpha=0.7, label='θ₂ᵈ')
    axes[1, 1].set_title('起重机关节角度')
    axes[1, 1].set_xlabel('时间 (s)')
    axes[1, 1].set_ylabel('角度 (deg)')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 起重机关节角速度
    axes[1, 2].plot(t, np.degrees(dq[:, 0]), 'b-', label='θ̇₁')
    axes[1, 2].plot(t, np.degrees(dq[:, 1]), 'r-', label='θ̇₂')
    axes[1, 2].set_title('起重机关节角速度')
    axes[1, 2].set_xlabel('时间 (s)')
    axes[1, 2].set_ylabel('角速度 (deg/s)')
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)
    
    # 起重机末端轨迹
    crane_model = floating_crane.crane_system['model']
    end_positions = []
    for i in range(len(t)):
        end_pos = crane_model.get_end_effector_position(q[i])
        end_positions.append(end_pos)
    end_positions = np.array(end_positions)
    
    axes[2, 0].plot(end_positions[:, 0], end_positions[:, 1], 'g-', linewidth=2, label='末端轨迹')
    axes[2, 0].plot(end_positions[0, 0], end_positions[0, 1], 'go', markersize=8, label='起点')
    axes[2, 0].plot(end_positions[-1, 0], end_positions[-1, 1], 'ro', markersize=8, label='终点')
    
    # 工作空间边界
    try:
        x_boundary, y_boundary = crane_model.get_workspace_boundary(50)
        axes[2, 0].plot(x_boundary, y_boundary, '--', color='gray', alpha=0.5, label='工作空间')
    except:
        pass
    
    axes[2, 0].set_title('起重机末端轨迹')
    axes[2, 0].set_xlabel('X (m)')
    axes[2, 0].set_ylabel('Y (m)')
    axes[2, 0].legend()
    axes[2, 0].set_aspect('equal')
    axes[2, 0].grid(True, alpha=0.3)
    
    # 浮体运动轨迹（俯视图）
    axes[2, 1].plot(eta[:, 0], eta[:, 1], 'b-', linewidth=2, label='浮体轨迹')
    axes[2, 1].plot(eta[0, 0], eta[0, 1], 'go', markersize=8, label='起点')
    axes[2, 1].plot(eta[-1, 0], eta[-1, 1], 'ro', markersize=8, label='终点')
    axes[2, 1].set_title('浮体运动轨迹（俯视图）')
    axes[2, 1].set_xlabel('X (m)')
    axes[2, 1].set_ylabel('Y (m)')
    axes[2, 1].legend()
    axes[2, 1].set_aspect('equal')
    axes[2, 1].grid(True, alpha=0.3)
    
    # 系统能量分析
    # 计算浮体动能
    vessel_kinetic = 0.5 * floating_crane.m * np.sum(v[:, :3]**2, axis=1)
    # 计算浮体势能（简化）
    vessel_potential = floating_crane.m * 9.81 * eta[:, 2]
    
    axes[2, 2].plot(t, vessel_kinetic, 'b-', label='浮体动能')
    axes[2, 2].plot(t, vessel_potential, 'r-', label='浮体势能')
    axes[2, 2].plot(t, vessel_kinetic + vessel_potential, 'g-', label='总能量')
    axes[2, 2].set_title('系统能量')
    axes[2, 2].set_xlabel('时间 (s)')
    axes[2, 2].set_ylabel('能量 (J)')
    axes[2, 2].legend()
    axes[2, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('floating_crane_simulation_results.png', dpi=150, bbox_inches='tight')
    plt.close()
    print("✓ 结果图表保存为 floating_crane_simulation_results.png")
    
    # 6. 生成动画帧（可选）
    print("\n6. 生成关键时刻的系统配置图...")
    
    # 选择几个关键时刻
    key_times = [0, 15, 30, 45, 60]
    key_indices = [np.argmin(np.abs(t - kt)) for kt in key_times if kt <= t[-1]]
    
    fig, axes = plt.subplots(1, len(key_indices), figsize=(4*len(key_indices), 4))
    if len(key_indices) == 1:
        axes = [axes]
    
    for i, idx in enumerate(key_indices):
        # 绘制浮体（简化为矩形）
        vessel_x = eta[idx, 0]
        vessel_y = eta[idx, 1]
        vessel_roll = eta[idx, 3]
        
        # 浮体轮廓
        vessel_length = 50
        vessel_width = 20
        vessel_corners = np.array([
            [-vessel_length/2, -vessel_width/2],
            [vessel_length/2, -vessel_width/2],
            [vessel_length/2, vessel_width/2],
            [-vessel_length/2, vessel_width/2],
            [-vessel_length/2, -vessel_width/2]
        ])
        
        # 旋转和平移
        cos_r, sin_r = np.cos(vessel_roll), np.sin(vessel_roll)
        rotation_matrix = np.array([[cos_r, -sin_r], [sin_r, cos_r]])
        vessel_rotated = vessel_corners @ rotation_matrix.T
        vessel_rotated[:, 0] += vessel_x
        vessel_rotated[:, 1] += vessel_y
        
        axes[i].plot(vessel_rotated[:, 0], vessel_rotated[:, 1], 'b-', linewidth=2, label='浮体')
        
        # 绘制起重机（简化）
        crane_base_x = vessel_x
        crane_base_y = vessel_y
        
        l1 = floating_crane.crane_system['config']['l1']
        l2 = floating_crane.crane_system['config']['l2']
        theta1, theta2 = q[idx]
        
        # 主臂末端
        arm1_end_x = crane_base_x + l1 * np.cos(theta1)
        arm1_end_y = crane_base_y + l1 * np.sin(theta1)
        
        # 副臂末端
        arm2_end_x = arm1_end_x + l2 * np.cos(theta1 + theta2)
        arm2_end_y = arm1_end_y + l2 * np.sin(theta1 + theta2)
        
        # 绘制起重机臂
        axes[i].plot([crane_base_x, arm1_end_x], [crane_base_y, arm1_end_y], 
                    'r-', linewidth=3, label='主臂')
        axes[i].plot([arm1_end_x, arm2_end_x], [arm1_end_y, arm2_end_y], 
                    'g-', linewidth=2, label='副臂')
        axes[i].plot(arm2_end_x, arm2_end_y, 'ko', markersize=8, label='负载')
        
        axes[i].set_title(f't = {t[idx]:.1f} s')
        axes[i].set_xlabel('X (m)')
        axes[i].set_ylabel('Y (m)')
        axes[i].set_aspect('equal')
        axes[i].grid(True, alpha=0.3)
        axes[i].legend()
        
        # 设置合适的显示范围
        all_x = [vessel_x - 30, vessel_x + 30, arm2_end_x]
        all_y = [vessel_y - 30, vessel_y + 30, arm2_end_y]
        axes[i].set_xlim(min(all_x) - 5, max(all_x) + 5)
        axes[i].set_ylim(min(all_y) - 5, max(all_y) + 5)
    
    plt.tight_layout()
    plt.savefig('floating_crane_configurations.png', dpi=150, bbox_inches='tight')
    plt.close()
    print("✓ 系统配置图保存为 floating_crane_configurations.png")
    
    print("\n🎉 浮吊系统仿真完成！")
    print("成功演示了6自由度浮体与双臂架起重机的耦合动力学仿真。")
    
    return {
        'time': t,
        'vessel_motion': eta,
        'vessel_velocity': v,
        'crane_angles': q,
        'crane_velocities': dq,
        'end_positions': end_positions,
        'performance_metrics': {
            'vessel_motion_rms': vessel_motion_rms,
            'crane_motion_rms': crane_motion_rms,
            'max_vessel_motion': max_vessel_motion,
            'max_crane_motion': max_crane_motion
        }
    }


if __name__ == "__main__":
    results = simulate_floating_crane()
