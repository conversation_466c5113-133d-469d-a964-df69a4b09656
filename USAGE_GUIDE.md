# 双臂架起重系统使用指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 激活虚拟环境
.\Floating2CranesEnv\Scripts\activate

# 安装依赖（核心包）
pip install numpy scipy sympy matplotlib plotly
```

### 2. 基础测试
```bash
# 最小功能测试
python examples/minimal_test.py

# 动力学推导测试
python examples/debug_dynamics.py

# 完整动力学测试
python examples/dynamics_test.py
```

### 3. 浮吊系统仿真
```bash
# 英文版本（推荐，无字体警告）
python examples/floating_crane_simulation_en.py

# 中文版本（修复字体）
python examples/floating_crane_simulation_fixed.py

# 字体配置工具
python examples/font_config.py
```

## 📁 文件说明

### 核心模块
- `src/dynamics/lagrangian_model.py` - 拉格朗日动力学基类
- `src/dynamics/crane_arm.py` - 双臂架起重机模型
- `src/control/controllers.py` - 控制器集合
- `src/simulation/simulator.py` - 仿真引擎
- `src/visualization/plotter.py` - 可视化工具
- `src/floating_crane_system.py` - 6DOF浮吊系统集成

### 示例文件
- `examples/minimal_test.py` - 最小功能测试
- `examples/debug_dynamics.py` - 动力学调试
- `examples/dynamics_test.py` - 动力学完整测试
- `examples/floating_crane_simulation_en.py` - 英文版浮吊仿真
- `examples/floating_crane_simulation_fixed.py` - 中文版浮吊仿真
- `examples/font_config.py` - 字体配置工具

## 🔧 基本用法

### 1. 创建双臂架起重机
```python
from src.dynamics.crane_arm import DoubleArmCrane

# 创建起重机
crane = DoubleArmCrane()

# 设置参数
crane.set_parameters(
    l1=2.0,      # 主臂长度
    l2=1.5,      # 副臂长度
    m1=50.0,     # 主臂质量
    m2=30.0,     # 副臂质量
    mL=100.0,    # 吊载质量
)

# 推导动力学
crane.derive_equations_of_motion()
```

### 2. 创建控制器
```python
from src.control.controllers import PDController

# PD控制器
controller = PDController(
    Kp=np.array([100, 50]),  # 比例增益
    Kd=np.array([20, 10])    # 微分增益
)
```

### 3. 执行仿真
```python
from src.simulation.simulator import CraneSimulator, SimulationConfig

# 创建仿真器
simulator = CraneSimulator(crane, controller)

# 仿真配置
config = SimulationConfig(t_end=10.0, dt=0.01)

# 执行仿真
results = simulator.simulate(initial_state, reference, time_vector, config)
```

### 4. 可视化结果
```python
from src.visualization.plotter import CraneVisualizer

# 创建可视化器
visualizer = CraneVisualizer(crane)

# 绘制结果
fig = visualizer.plot_simulation_results(results)

# 创建动画
anim = visualizer.animate_simulation(results)
```

### 5. 浮吊系统集成
```python
from src.floating_crane_system import create_default_floating_crane

# 创建默认浮吊系统
floating_crane = create_default_floating_crane()

# 执行仿真
solution = solve_ivp(floating_crane.dynamics, [0, 60], initial_state)
```

## 📊 输出文件

### 仿真结果图表
- `floating_crane_simulation_results_en.png` - 英文版仿真结果
- `floating_crane_simulation_results_cn.png` - 中文版仿真结果
- `floating_crane_configurations_en.png` - 英文版系统配置
- `floating_crane_configurations_cn.png` - 中文版系统配置

### 测试结果
- `dynamics_test_results.png` - 动力学测试结果
- `target_configuration.png` - 目标配置图
- `test_configuration.png` - 测试配置图
- `font_test.png` - 字体测试图

## ⚙️ 参数配置

### 起重机参数
```python
crane_params = {
    'l1': 20.0,      # 主臂长度 (m)
    'l2': 15.0,      # 副臂长度 (m)
    'm1': 800.0,     # 主臂质量 (kg)
    'm2': 500.0,     # 副臂质量 (kg)
    'mL': 2000.0,    # 吊载质量 (kg)
    'I1': 2000.0,    # 主臂转动惯量 (kg⋅m²)
    'I2': 1000.0,    # 副臂转动惯量 (kg⋅m²)
    'g': 9.81,       # 重力加速度 (m/s²)
    'lc1': 7.5,      # 主臂质心距离 (m)
    'lc2': 5.0,      # 副臂质心距离 (m)
}
```

### 浮体参数
```python
vessel_params = {
    'mass': 50000.0,  # 浮体质量 (kg)
    'inertia': np.diag([1e6, 2e6, 1.5e6]),  # 转动惯量
    'damping': np.diag([1000, 1000, 2000, 5000, 8000, 3000]),  # 阻尼
    'added_mass': np.diag([5000, 5000, 10000, 2000, 5000, 1000]),  # 附加质量
    'hydrostatic': np.diag([0, 0, 50000, 200000, 300000, 0])  # 静力恢复
}
```

### 控制参数
```python
control_params = {
    'controller_type': 'PD',  # 'PD' 或 'ComputedTorque'
    'Kp': np.array([2000, 1000]),  # 比例增益
    'Kd': np.array([400, 200]),    # 微分增益
}
```

## 🔍 故障排除

### 1. 字体警告问题
**问题**: 出现中文字体警告
**解决**: 
- 使用英文版仿真脚本 `floating_crane_simulation_en.py`
- 或运行字体配置工具 `font_config.py`

### 2. 符号推导缓慢
**问题**: 动力学推导时间过长
**解决**: 
- 使用简化参数进行测试
- 跳过符号简化步骤

### 3. 仿真不稳定
**问题**: 数值积分发散
**解决**: 
- 减小时间步长
- 调整控制器增益
- 检查初始条件

### 4. 导入错误
**问题**: 模块导入失败
**解决**: 
- 确保在正确目录运行
- 检查Python路径设置
- 安装缺失的依赖包

## 📈 性能优化

### 1. 仿真速度
- 使用较大的时间步长（如0.1s）
- 选择合适的积分方法（RK45）
- 避免过于复杂的控制器

### 2. 内存使用
- 限制仿真时间长度
- 减少输出数据频率
- 及时释放大型数组

### 3. 精度平衡
- 根据需要调整积分容差
- 平衡计算精度和速度
- 使用合适的数值方法

## 🎯 应用建议

### 1. 工程应用
- 根据实际起重机参数调整模型
- 考虑实际的环境扰动
- 验证控制器的鲁棒性

### 2. 研究用途
- 尝试不同的控制策略
- 分析系统的动力学特性
- 进行参数敏感性分析

### 3. 教学演示
- 使用可视化功能展示结果
- 对比不同控制方法的效果
- 分析系统的物理行为

---

**如有问题，请参考示例代码或查看项目文档。**
