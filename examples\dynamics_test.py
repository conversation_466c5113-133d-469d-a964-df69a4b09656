"""
动力学测试
Dynamics Test

测试动力学推导和仿真功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
import matplotlib.pyplot as plt

def test_dynamics():
    """测试动力学功能"""
    print("=== 动力学推导和仿真测试 ===\n")
    
    try:
        # 1. 创建起重机系统
        print("1. 创建起重机系统...")
        from dynamics.crane_arm import DoubleArmCrane
        
        crane = DoubleArmCrane()
        crane.set_parameters(
            l1=2.0, l2=1.5, m1=50.0, m2=30.0, mL=100.0,
            I1=20.0, I2=10.0, g=9.81, lc1=1.0, lc2=0.75
        )
        print("✓ 起重机系统创建成功")
        
        # 2. 推导动力学方程
        print("\n2. 推导动力学方程...")
        L = crane.derive_lagrangian()
        print("✓ 拉格朗日量推导完成")
        
        EOM = crane.derive_equations_of_motion()
        print("✓ 运动方程推导完成")
        
        print(f"  质量矩阵维度: {crane.M_symbolic.shape}")
        print(f"  科里奥利矩阵维度: {crane.C_symbolic.shape}")
        print(f"  重力向量维度: {crane.G_symbolic.shape}")
        
        # 3. 创建控制器
        print("\n3. 创建控制器...")
        from control.controllers import PDController
        
        Kp = np.array([100, 50])
        Kd = np.array([20, 10])
        controller = PDController(Kp, Kd)
        print("✓ PD控制器创建成功")
        
        # 4. 生成轨迹
        print("\n4. 生成参考轨迹...")
        from simulation.simulator import TrajectoryGenerator
        
        t_end = 3.0
        dt = 0.02
        t = np.arange(0, t_end, dt)
        
        initial_pos = np.array([0.0, 0.0])
        target_pos = np.array([np.pi/6, np.pi/8])
        
        reference = TrajectoryGenerator.ramp_trajectory(
            t, initial_pos, target_pos, ramp_duration=1.5
        )
        print(f"✓ 轨迹生成成功，维度: {reference.shape}")
        
        # 5. 执行仿真
        print("\n5. 执行仿真...")
        from simulation.simulator import CraneSimulator, SimulationConfig
        
        config = SimulationConfig(t_end=t_end, dt=dt, method='RK45')
        simulator = CraneSimulator(crane, controller)
        
        initial_state = np.array([0.0, 0.0, 0.0, 0.0])
        
        results = simulator.simulate(initial_state, reference, t, config)
        print(f"✓ 仿真完成！计算时间: {results.computation_time:.3f}s")
        
        # 6. 分析结果
        print("\n6. 分析仿真结果...")
        metrics = results.performance_metrics
        
        print(f"  RMS跟踪误差: {metrics['rms_position_error']}")
        print(f"  最大跟踪误差: {metrics['max_position_error']}")
        print(f"  总控制能量: {metrics['total_control_energy']:.2f} J")
        print(f"  摆动幅度: {metrics['swing_magnitude']:.4f} m/s")
        
        # 7. 生成图表
        print("\n7. 生成结果图表...")
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 关节角度
        axes[0, 0].plot(t, results.states[:, 0], 'b-', label='θ₁')
        axes[0, 0].plot(t, results.states[:, 1], 'r-', label='θ₂')
        axes[0, 0].plot(t, reference[:, 0], 'b--', alpha=0.7, label='θ₁ᵈ')
        axes[0, 0].plot(t, reference[:, 1], 'r--', alpha=0.7, label='θ₂ᵈ')
        axes[0, 0].set_title('关节角度')
        axes[0, 0].set_xlabel('时间 (s)')
        axes[0, 0].set_ylabel('角度 (rad)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 关节角速度
        axes[0, 1].plot(t, results.states[:, 2], 'b-', label='θ̇₁')
        axes[0, 1].plot(t, results.states[:, 3], 'r-', label='θ̇₂')
        axes[0, 1].set_title('关节角速度')
        axes[0, 1].set_xlabel('时间 (s)')
        axes[0, 1].set_ylabel('角速度 (rad/s)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 控制力矩
        axes[1, 0].plot(t, results.controls[:, 0], 'b-', label='τ₁')
        axes[1, 0].plot(t, results.controls[:, 1], 'r-', label='τ₂')
        axes[1, 0].set_title('控制力矩')
        axes[1, 0].set_xlabel('时间 (s)')
        axes[1, 0].set_ylabel('力矩 (N⋅m)')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 末端轨迹
        end_positions = metrics['end_positions']
        axes[1, 1].plot(end_positions[:, 0], end_positions[:, 1], 'g-', linewidth=2, label='末端轨迹')
        axes[1, 1].plot(end_positions[0, 0], end_positions[0, 1], 'go', markersize=8, label='起点')
        axes[1, 1].plot(end_positions[-1, 0], end_positions[-1, 1], 'ro', markersize=8, label='终点')
        
        # 工作空间边界
        try:
            x_boundary, y_boundary = crane.get_workspace_boundary(50)
            axes[1, 1].plot(x_boundary, y_boundary, '--', color='gray', alpha=0.5, label='工作空间')
        except:
            pass
        
        axes[1, 1].set_title('末端轨迹')
        axes[1, 1].set_xlabel('X (m)')
        axes[1, 1].set_ylabel('Y (m)')
        axes[1, 1].legend()
        axes[1, 1].set_aspect('equal')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('dynamics_test_results.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("✓ 结果图表保存为 dynamics_test_results.png")
        
        # 8. 测试可视化
        print("\n8. 测试可视化功能...")
        from visualization.plotter import CraneVisualizer
        
        visualizer = CraneVisualizer(crane)
        
        # 绘制初始和最终配置
        fig_config = visualizer.plot_static_configuration(target_pos, "目标配置")
        plt.savefig('target_configuration.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("✓ 配置图保存为 target_configuration.png")
        
        print("\n🎉 动力学测试全部通过！")
        print("系统能够正确推导动力学方程并执行仿真。")
        
        return True
        
    except Exception as e:
        print(f"❌ 动力学测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workspace_analysis():
    """测试工作空间分析"""
    print("\n=== 工作空间分析测试 ===")
    
    try:
        from dynamics.crane_arm import DoubleArmCrane
        
        crane = DoubleArmCrane()
        crane.set_parameters()
        
        # 工作空间边界
        x_boundary, y_boundary = crane.get_workspace_boundary()
        print(f"✓ 工作空间边界计算成功，点数: {len(x_boundary)}")
        
        # 逆运动学测试
        test_positions = [
            [2.0, 1.0],   # 可达位置
            [1.5, 2.0],   # 可达位置
            [5.0, 0.0],   # 不可达位置
        ]
        
        for pos in test_positions:
            angles, success = crane.inverse_kinematics(np.array(pos))
            print(f"  位置 {pos}: 可达={success}, 角度={angles}")
        
        print("✓ 工作空间分析测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 工作空间分析失败: {e}")
        return False

if __name__ == "__main__":
    print("双臂架起重系统动力学测试\n")
    
    # 主要动力学测试
    if test_dynamics():
        # 工作空间分析测试
        test_workspace_analysis()
        
        print("\n✅ 所有测试完成！")
        print("双臂架起重系统框架功能完整，可以用于实际仿真。")
    else:
        print("\n❌ 测试失败，请检查代码。")
